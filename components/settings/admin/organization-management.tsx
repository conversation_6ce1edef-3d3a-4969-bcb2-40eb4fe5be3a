'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import apiClient from '@/lib/apiClient'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'

export interface OrganizationManagementProps {
  organization: {
    id: string
    name: string
  }
  onSubmit: (org: { id: string; name: string }) => void
  onClose: () => void
}

export default function ManageOrganization({
  organization,
  onSubmit,
  onClose,
}: OrganizationManagementProps) {
  const [formData, setFormData] = useState({ name: '' })
  const [originalName, setOriginalName] = useState('')
  const [isValid, setIsValid] = useState(false)

  // Fetch current org data
  useEffect(() => {
    const fetchOrganization = async () => {
      try {
        const res = await apiClient.get("/organization")
        const org = res.data.find((org: any) => org.id === organization.id)
        setOriginalName(org?.name || '')
        setFormData({ name: org?.name || '' })
      } catch (error) {
        console.error('Failed to fetch organization:', error)
        toast.error("Failed to fetch organization")
      }
    }

    fetchOrganization()
  }, [organization.id])


  useEffect(() => {
    setIsValid(formData.name.trim().length > 0 && formData.name !== originalName)
  }, [formData.name, originalName])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ name: e.target.value })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const payload = { name: formData.name.trim() }

    try {
      await apiClient.patch(`/organization/${organization.id}`, payload)
      // Backend returns: { success: true, message: "Organization updated successfully" }
      toast.success('Organization updated successfully')
      onSubmit({ id: organization.id, name: formData.name.trim() })
      onClose()
    } catch (error) {
      console.error('Update error:', error)
      toast.error('Failed to update organization')
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black bg-opacity-60" />
      <div className="relative z-10 bg-white p-6 rounded-lg border shadow-sm w-[400px]">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Edit Organization</h1>
          <p className="text-gray-600 mt-2">
            Update organization name for <strong>{originalName || 'Unknown'}</strong>.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Organization Name</label>
            <Input
              value={formData.name}
              name="name"
              onChange={handleChange}
              placeholder="Enter organization name"
            />
          </div>
          <div className="mt-4 space-y-2">
            <Button type="submit" className="w-full" disabled={!isValid}>
              Save Changes
            </Button>
            <Button variant="ghost" onClick={onClose} className="w-full">
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
