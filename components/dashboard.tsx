"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";
import { format, startOfHour, differenceInHours } from "date-fns";

import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

interface CustomerType {
  id: number;
  first_name: string;
  last_name: string;
  username: string;
  password: string;
  contact: string[];
  email: string;
  address: string;
  status: string;
  package: string;
  registered: string;
  expiration: string;
  organization?: string;
}
interface AnalyticsEntry {
  id: number;
  count: number;
  data: Array<{
    username?: string;
    acctstarttime?: string;
    acctinputoctets?: number;
    acctoutputoctets?: number;
  }>;
  created_at: number;
  inputMbps?: number;
  outputMbps?: number;
}

interface GuestType {
  id: number;
  data: {
    name: string;
    email: string;
    company: string;
  };
}

type CustomerFilter = "All" | "Active" | "Inactive" | "Expired";

export default function DashboardPage() {
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const router = useRouter();
  const access_token = Cookies.get("accessToken");

  const [allCustomers, setAllCustomers] = useState<CustomerType[]>([]);
  const [clientCount, setClientCount] = useState<AnalyticsEntry[]>([]);
  const [guests, setGuests] = useState<GuestType[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingGuests, setLoadingGuests] = useState(true);
  const [errorCustomers, setErrorCustomers] = useState<string | null>(null);
  const [errorGuests, setErrorGuests] = useState<string | null>(null);
  const [selectedHours, setSelectedHours] = useState(1);
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(new Date());

  // Active clients count from last data point
  const activeCustomers = clientCount.at(-1)?.count ?? 0;

  useEffect(() => {
    if (!access_token) {
      setLoadingCustomers(false);
      return;
    }
    setLoadingCustomers(true);
    setErrorCustomers(null);
    apiClient
      .get("/customer", {
        headers: { Authorization: `Bearer ${access_token}` },
        cache: "no-store",
      })
      .then((response) => {
        // Backend returns: { success: true, message: "List Customer", data: [...] }
        // The apiClient response interceptor extracts the data property
        setAllCustomers(response?.data || []);
      })
      .catch((error) => {
        console.error("Customer fetch error:", error);
        setErrorCustomers(error.message || "Failed to load customer data.");
      })
      .finally(() => setLoadingCustomers(false));
  }, [access_token]);

  useEffect(() => {
    const now = new Date();
    const start = new Date(now.getTime() - selectedHours * 60 * 60 * 1000);
    setStartDate(start);
    setEndDate(now);

    const formattedStart = format(start, "yyyy-MM-dd HH:mm:ss");
    const formattedEnd = format(now, "yyyy-MM-dd HH:mm:ss");

    if (!access_token) return;

    apiClient
      .post(
        "/analytics",
        { created_from: formattedStart, created_to: formattedEnd },
        {
          headers: { Authorization: `Bearer ${access_token}` },
        }
      )
      .then((response) => {
        // Backend returns: { success: true, message: "Analytics", data: [...] }
        // The apiClient response interceptor extracts the data property
        const dataWithTimestamp = (response?.data?.data || []).map((entry) => ({
          ...entry,
          created_at: new Date(entry.created_at).getTime(),
        }));
        console.log(`Analytics data for ${selectedHours}h:`, dataWithTimestamp);
        setClientCount(dataWithTimestamp);
      })
      .catch((error) => {
        console.error("Analytics fetch error:", error);
        toast.error(error.message || "Failed to fetch client analytics");
      });
  }, [selectedHours, access_token]);

  useEffect(() => {
    if (!access_token) {
      setLoadingGuests(false);
      return;
    }
    setLoadingGuests(true);
    setErrorGuests(null);
    apiClient
      .get("/guest/voucher/?type=guest", {
        headers: { Authorization: `Bearer ${access_token}` },
        cache: "no-store",
      })
      .then((response) => {
        // Backend returns: { success: true, data: [...] }
        // The apiClient response interceptor extracts the data property
        setGuests(response?.data?.data || []);
      })
      .catch((error) => {
        console.error("Guest fetch error:", error);
        setErrorGuests(error.message || "Failed to load guest data.");
      })
      .finally(() => setLoadingGuests(false));
  }, [access_token]);

  // Generate fallback data when no real data is available
  const generateFallbackData = (): AnalyticsEntry[] => {
    const now = new Date();
    const start = new Date(now.getTime() - selectedHours * 60 * 60 * 1000);
    const fallbackData: AnalyticsEntry[] = [];

    let intervalMs: number;
    if (selectedHours === 1) {
      intervalMs = 5 * 60 * 1000; // 5 minutes
    } else if (selectedHours === 6) {
      intervalMs = 30 * 60 * 1000; // 30 minutes
    } else {
      intervalMs = 60 * 60 * 1000; // 1 hour
    }

    // Round start time to the nearest interval boundary to align with ticks
    const roundedStart = Math.floor(start.getTime() / intervalMs) * intervalMs;
    const roundedEnd = Math.ceil(now.getTime() / intervalMs) * intervalMs;

    for (let ts = roundedStart; ts <= roundedEnd; ts += intervalMs) {
      fallbackData.push({
        id: 0,
        count: 0,
        data: [],
        created_at: ts,
        inputMbps: 0,
        outputMbps: 0,
      });
    }

    return fallbackData;
  };

  // Generate ticks at intervals based on clientCount data range
  const generateTicks = (): number[] => {
    const dataToUse = clientCount.length > 0 ? clientCount : generateFallbackData();
    if (!dataToUse.length) return [];

    const minTime = new Date(Math.min(...dataToUse.map((d: AnalyticsEntry) => d.created_at)));
    const maxTime = new Date(Math.max(...dataToUse.map((d: AnalyticsEntry) => d.created_at)));
    const ticks: number[] = [];

    let intervalMs: number;

    if (selectedHours === 1) {
      intervalMs = 5 * 60 * 1000; // 5 minutes
    } else if (selectedHours === 6) {
      intervalMs = 30 * 60 * 1000; // 30 minutes
    } else {
      intervalMs = 60 * 60 * 1000; // 1 hour
    }

    // Use the same rounding logic as fallback data generation
    const start = Math.floor(minTime.getTime() / intervalMs) * intervalMs;
    const end = Math.ceil(maxTime.getTime() / intervalMs) * intervalMs;

    for (let ts = start; ts <= end; ts += intervalMs) {
      ticks.push(ts);
    }

    return ticks;
  };

  const ticks = generateTicks();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload?.length) {
      const point = payload.reduce((acc: any, item: any) => {
        acc[item.dataKey] = item.payload[item.dataKey];
        acc.created_at = item.payload.created_at;
        return acc;
      }, {});

      return (
        <div className="bg-white p-2 border border-gray-300 rounded shadow text-xs">
          <p className="text-gray-600">
            {new Date(point.created_at).toLocaleString([], {
              day: "2-digit",
              year: "2-digit",
              month: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </p>
          {point.count !== undefined && (
            <p className="text-black font-semibold">
              {point.count} Online client{point.count > 1 ? "s" : ""}
            </p>
          )}
          {point.inputMbps !== undefined && (
            <p className="text-black font-semibold">
              Input (Upload): {Number(point.inputMbps).toFixed(2)} Mbps
            </p>
          )}
          {point.outputMbps !== undefined && (
            <p className="text-black font-semibold">
              Output (Download): {Number(point.outputMbps).toFixed(2)} Mbps
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="flex flex-1 overflow-hidden">
      <main className="flex-1 overflow-auto p-4">
        {/* Stats Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          {/* Active Clients Card */}
          <div
            className="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg text-white cursor-pointer hover:shadow-lg hover:from-green-600 hover:to-green-700 transition-all duration-200"
            onClick={() => router.push('/app/analytics?type=activesessions&tab=users')}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-xs">Online Clients</p>
                <p className="text-xl font-bold">{activeCustomers}</p>
                <p className="text-xs text-green-100">Currently active</p>
              </div>
              <div className="h-8 w-8 text-green-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="m22 21-3-3m0 0-3-3m3 3 3-3m-3 3-3 3"/>
                </svg>
              </div>
            </div>
          </div>

          {/* Total Staff Card */}
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-lg text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-xs">Total Staff</p>
                <p className="text-xl font-bold">{allCustomers.length}</p>
                <p className="text-xs text-blue-100">Registered users</p>
              </div>
              <div className="h-8 w-8 text-blue-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="m22 21-3-3m0 0-3-3m3 3 3-3m-3 3-3 3"/>
                </svg>
              </div>
            </div>
          </div>

          {/* Total Guests Card */}
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-lg text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-xs">Total Guests</p>
                <p className="text-xl font-bold">{guests.length}</p>
                <p className="text-xs text-purple-100">Guest accounts</p>
              </div>
              <div className="h-8 w-8 text-purple-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="M20 8v6M23 11h-6"/>
                </svg>
              </div>
            </div>
          </div>

          {/* <div className="bg-white text-orange-700 p-4 rounded-lg shadow-md border border-orange-300">
            <div>
              <h3 className="text-sm font-medium text-orange-600">
                Bandwidth Usage
              </h3>
              <p className="text-2xl font-bold text-orange-800">--</p>
            </div>
          </div> */}

        </div>

        <div className="bg-white p-4 rounded-lg shadow border mb-4">
          <h2 className="text-lg font-semibold mb-3 text-gray-900">
            Time Range Selection
          </h2>
          <div className="flex flex-wrap gap-2">
            {[
              { value: 1, label: "1 Hr" },
              { value: 6, label: "6 Hrs" },
              { value: 12, label: "12 Hrs" },
              { value: 24, label: "1 Day" },
              { value: 120, label: "5 Days" },
              { value: 168, label: "1 Week" },
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setSelectedHours(option.value)}
                className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${
                  selectedHours === option.value
                    ? "bg-blue-600 text-white shadow-md"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          {/* Online Client Graph */}
          <div className="bg-white p-4 rounded-lg shadow border">
            <h2 className="text-lg font-semibold mb-3 text-gray-900">Online Client Activity</h2>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={clientCount.length > 0 ? clientCount : generateFallbackData()}
                  margin={{ top: 0, right: 20, left: 0, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="created_at"
                    type="number"
                    scale="time"
                    domain={clientCount.length > 0 ? ["dataMin", "dataMax"] : ["auto", "auto"]}
                    ticks={ticks}
                    tickFormatter={(tick) => format(new Date(tick), "HH:mm")}
                    tick={{ fontSize: 10 }}
                    minimumTickGap={10}
                  />
                  <YAxis
                    domain={[0, "auto"]}
                    tickFormatter={(tick) => (tick === 0 ? "0" : tick)}
                    tick={{ fontSize: 10 }}
                    allowDecimals={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend
                    iconSize={12}
                    wrapperStyle={{ fontSize: '12px' }}
                    payload={[{ value: "Online Clients", color: "#16a34a" }]}
                  />
                  <Line
                    type="step"
                    dataKey="count"
                    stroke="#16a34a"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{
                      r: 4,
                      fill: "#16a34a",
                      stroke: "#fff",
                      strokeWidth: 2,
                    }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Bandwidth Graph */}
          <div className="bg-white p-4 rounded-lg shadow border">
            <h2 className="text-lg font-semibold mb-3 text-gray-900">Bandwidth Usage</h2>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart 
                  data={clientCount.length > 0 ? clientCount : generateFallbackData()} 
                  margin={{ top: 0, right: 20, left: 0, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="created_at"
                    type="number"
                    scale="time"
                    domain={clientCount.length > 0 ? ["dataMin", "dataMax"] : ["auto", "auto"]}
                    ticks={ticks}
                    tickFormatter={(tick) => format(new Date(tick), "HH:mm")}
                    tick={{ fontSize: 10 }}
                  />
                  <YAxis 
                    tickFormatter={(tick) => (tick === 0 ? "" : tick)} 
                    tick={{ fontSize: 10 }} 
                    allowDecimals={false} 
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend 
                    iconSize={12}
                    wrapperStyle={{ fontSize: '12px' }}
                    payload={[
                      { value: "Download", color: "#8b5cf6" }, 
                      { value: "Upload", color: "#10b981" }
                    ]} 
                  />
                  <Line
                    type="monotone"
                    dataKey="inputMbps"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#10b981", stroke: "#fff", strokeWidth: 2 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="outputMbps"
                    stroke="#8b5cf6"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#8b5cf6", stroke: "#fff", strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Tables Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          {/* Recently Connected Guests Table */}
          <div className="bg-white rounded-lg shadow border">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recently Connected Guests</h2>
              <p className="text-sm text-gray-600">Latest guest connections</p>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full text-xs">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loadingGuests ? (
                    <tr>
                      <td colSpan={3} className="text-center py-6 text-gray-500">
                        Loading guest data...
                      </td>
                    </tr>
                  ) : errorGuests ? (
                    <tr>
                      <td colSpan={3} className="text-center py-6 text-red-600">
                        {errorGuests}
                      </td>
                    </tr>
                  ) : guests.length > 0 ? (
                    guests.slice(0, 7).map((guest) => (
                      <tr key={guest.id} className="hover:bg-gray-50">
                        <td className="px-4 py-2 text-gray-900 whitespace-nowrap">
                          {guest.data.name}
                        </td>
                        <td className="px-4 py-2 text-gray-600 whitespace-nowrap">
                          {guest.data.email}
                        </td>
                        <td className="px-4 py-2 text-gray-600 whitespace-nowrap">
                          {guest.data.company}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="text-center py-6 text-gray-500">
                        No recent guest connections found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Quick Stats or Coming Soon */}
          <div className="bg-white p-4 rounded-lg shadow border">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Quick Stats</h2>
              <p className="text-sm text-gray-600">Overview metrics</p>
            </div>
            <div className="p-4">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Active Sessions</span>
                  <span className="text-xl font-bold text-green-600">{activeCustomers}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Total Users</span>
                  <span className="text-xl font-bold text-blue-600">{allCustomers.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Guest Accounts</span>
                  <span className="text-xl font-bold text-purple-600">{guests.length}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recently Connected Users Table */}
        <div className="bg-white rounded-lg shadow border">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recently Connected Users</h2>
            <p className="text-sm text-gray-600">Latest staff user connections</p>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-xs">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loadingCustomers ? (
                  <tr>
                    <td colSpan={3} className="text-center py-6 text-gray-500">
                      Loading user data...
                    </td>
                  </tr>
                ) : errorCustomers ? (
                  <tr>
                    <td colSpan={3} className="text-center py-6 text-red-600">
                      {errorCustomers}
                    </td>
                  </tr>
                ) : allCustomers.length > 0 ? (
                  allCustomers.slice(0, 7).map((customer) => (
                    <tr key={customer.id} className="hover:bg-gray-50">
                      <td className="px-4 py-2 text-gray-900 whitespace-nowrap">
                        {customer.first_name} {customer.last_name}
                      </td>
                      <td className="px-4 py-2 text-gray-600 whitespace-nowrap">
                        {customer.email}
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            customer.status === "Active"
                              ? "bg-green-100 text-green-800"
                              : customer.status === "Inactive"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {customer.status}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} className="text-center py-6 text-gray-500">
                      No users found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>
  );
}
