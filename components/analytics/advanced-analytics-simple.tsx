"use client";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { But<PERSON> } from "../ui/button";
import { ChevronLeft, ChevronRight, Sessions, Users, Network, Activity, Router } from "@/components/icons/list";
import { useSearchParams, useRouter } from "next/navigation";
import { DepartmentStats, PackageInfo, NAS } from "@/types/interface-type";
import {
  Bar<PERSON>hart,
  LineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  RadialBarChart,
  RadialBar,
  Scatter<PERSON>hart,
  Scatter,
  LabelList,
  ComposedChart,
} from "recharts";

// Utility functions
const getDefaultDates = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 7);
  const today = new Date();
  return {
    start: yesterday.toISOString().split("T")[0],
    end: today.toISOString().split("T")[0]
  };
};

const formatBytes = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export default function AdvancedAnalyticsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultDates = getDefaultDates();

  // Basic state
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"overview" | "users" | "departments" | "nas">("overview");
  const [startDate, setStartDate] = useState(defaultDates.start);
  const [endDate, setEndDate] = useState(defaultDates.end);

  // Basic data states
  const [departments, setDepartments] = useState<PackageInfo[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [individualStats, setIndividualStats] = useState<DepartmentStats[]>([]);
  const [overviewStats, setOverviewStats] = useState({
    totalActiveUsers: 0,
    totalStaffUsers: 0,
    totalGuestUsers: 0,
    totalSessions: 0,
    totalBandwidthGB: 0,
    avgSessionDuration: 0,
    peakHourUsage: "12:00",
    departmentCount: 0,
  });

  // Additional state for charts
  const [departmentPieData, setDepartmentPieData] = useState<any[]>([]);
  const [usersPieData, setUsersPieData] = useState<any[]>([]);
  const [hourlyUsageData, setHourlyUsageData] = useState<any[]>([]);
  const [trendData, setTrendData] = useState<any[]>([]);
  const [nasDevices, setNasDevices] = useState<NAS[]>([]);
  const [nasAnalytics, setNasAnalytics] = useState<any[]>([]);

  // Basic fetch function
  const fetchDepartments = async () => {
    try {
      const res = await apiClient.get("/package");
      const deptData = res?.data || [];
      setDepartments(deptData);

      // Update overview stats
      setOverviewStats(prev => ({
        ...prev,
        departmentCount: deptData.length
      }));
    } catch (err) {
      console.error("Failed to fetch departments:", err);
    }
  };

  const fetchCustomers = async () => {
    try {
      const res = await apiClient.get("/customer");
      setCustomers(res?.data || []);
    } catch (err) {
      console.error("Failed to fetch customers:", err);
    }
  };

  const fetchNasDevices = async () => {
    try {
      const res = await apiClient.get("/nas");
      const nasData = res?.data || [];
      setNasDevices(nasData);
    } catch (err) {
      console.error("Failed to fetch NAS devices:", err);
    }
  };

  const fetchDepartmentStats = async () => {
    if (departments.length === 0) return;

    setLoading(true);
    try {
      const payload = {
        group: departments.map((d) => d.package_name),
        start: `${startDate} 00:00:00`,
        end: `${endDate} 23:59:59`
      };

      const res = await apiClient.post("/department/stats", payload);
      const rawData = res?.data || [];

      setIndividualStats(rawData);

      // Process basic overview stats
      const uniqueUsers = new Set(rawData.map((d: DepartmentStats) => d.username));
      const customerUsernames = new Set(customers.map(c => c.username));
      const staffUsers = Array.from(uniqueUsers).filter(u => customerUsernames.has(u));
      const guestUsers = Array.from(uniqueUsers).filter(u => !customerUsernames.has(u));

      const totalBandwidth = rawData.reduce((sum: number, d: DepartmentStats) =>
        sum + (Number(d.acctinputoctets) + Number(d.acctoutputoctets)), 0) / (1024 * 1024 * 1024);

      setOverviewStats({
        totalActiveUsers: uniqueUsers.size,
        totalStaffUsers: staffUsers.length,
        totalGuestUsers: guestUsers.length,
        totalSessions: rawData.length,
        totalBandwidthGB: totalBandwidth,
        avgSessionDuration: 0,
        peakHourUsage: "12:00",
        departmentCount: departments.length,
      });

      // Process department pie data
      const deptUsage = new Map<string, number>();
      rawData.forEach((stat: DepartmentStats) => {
        const usage = (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)) / (1024 * 1024 * 1024);
        deptUsage.set(stat.groupname, (deptUsage.get(stat.groupname) || 0) + usage);
      });

      const colors = ["#3b82f6", "#10b981", "#8b5cf6", "#ef4444", "#06b6d4", "#84cc16", "#f97316", "#e879f9"];
      const deptPieData = Array.from(deptUsage.entries())
        .map(([name, value], index) => ({
          name,
          value: parseFloat(value.toFixed(2)),
          fill: colors[index % colors.length]
        }))
        .filter(item => item.value > 0)
        .sort((a, b) => b.value - a.value);

      setDepartmentPieData(deptPieData);

      // Process users pie data (top 10 users)
      const userUsage = new Map<string, any>();
      rawData.forEach((stat: DepartmentStats) => {
        const usage = (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)) / (1024 * 1024 * 1024);
        const username = stat.username;
        const department = stat.groupname || 'Unknown Department';

        if (!userUsage.has(username)) {
          userUsage.set(username, {
            username,
            department,
            usage: 0,
            sessions: 0
          });
        }

        const userData = userUsage.get(username);
        userData.usage += usage;
        userData.sessions++;
        // Update department if we get a non-empty one
        if (stat.groupname && stat.groupname.trim()) {
          userData.department = stat.groupname;
        }
      });

      const topUsers = Array.from(userUsage.values())
        .map((userData, index) => ({
          name: userData.username,
          displayName: `${userData.username} (${userData.department})`,
          value: parseFloat(userData.usage.toFixed(2)),
          department: userData.department,
          sessions: userData.sessions,
          fill: colors[index % colors.length]
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 10);

      setUsersPieData(topUsers);

      // Process hourly usage data
      const hourlyUsage = new Array(24).fill(0);
      const hourlySessions = new Array(24).fill(0);

      rawData.forEach((stat: DepartmentStats) => {
        const hour = new Date(stat.acctstarttime).getHours();
        const usage = (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)) / (1024 * 1024);
        hourlyUsage[hour] += usage;
        hourlySessions[hour]++;
      });

      const hourlyData = hourlyUsage.map((usage, hour) => ({
        hour: hour.toString().padStart(2, '0') + ':00',
        usage: parseFloat(usage.toFixed(2)),
        sessions: hourlySessions[hour]
      }));

      setHourlyUsageData(hourlyData);

      // Process trend data (daily usage over the selected date range)
      const trendUsage = new Map<string, any>();
      rawData.forEach((stat: DepartmentStats) => {
        const date = new Date(stat.acctstarttime).toISOString().split('T')[0];
        const usage = (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)) / (1024 * 1024 * 1024);

        if (!trendUsage.has(date)) {
          trendUsage.set(date, { date, usage: 0, sessions: 0, users: new Set() });
        }

        const dayData = trendUsage.get(date);
        dayData.usage += usage;
        dayData.sessions++;
        dayData.users.add(stat.username);
      });

      const trendArray = Array.from(trendUsage.values())
        .map(item => ({
          date: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          usage: parseFloat(item.usage.toFixed(2)),
          sessions: item.sessions,
          users: item.users.size
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      setTrendData(trendArray);

      // Process NAS analytics
      if (nasDevices.length > 0) {
        const nasUsage = new Map<string, any>();

        nasDevices.forEach(nas => {
          const nasStats = rawData.filter((stat: DepartmentStats) => stat.nasipaddress === nas.nasname);
          const totalSessions = nasStats.length;
          const totalBandwidth = nasStats.reduce((sum, stat) =>
            sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / (1024 * 1024 * 1024);

          nasUsage.set(nas.nasname, {
            nasName: nas.name || nas.nasname,
            nasIP: nas.nasname,
            totalSessions,
            totalBandwidth: parseFloat(totalBandwidth.toFixed(2)),
            vendor: nas.vendor || 'Unknown',
            description: nas.description || 'No description'
          });
        });

        setNasAnalytics(Array.from(nasUsage.values()));
      }
    } catch (err) {
      console.error("Error fetching department stats:", err);
      toast.error("Error fetching analytics data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDepartments();
    fetchCustomers();
    fetchNasDevices();
  }, []);

  useEffect(() => {
    if (departments.length > 0) {
      fetchDepartmentStats();
    }
  }, [departments, customers, startDate, endDate]);

  const handleTabChange = (tab: "overview" | "users" | "departments" | "nas") => {
    setActiveTab(tab);
    const params = new URLSearchParams(searchParams.toString());
    params.set("tab", tab);
    router.replace(`?${params.toString()}`);
  };

  return (
    <div className="p-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Advanced Analytics</h1>
          <p className="text-sm text-sm text-gray-600 mt-1">Network usage insights and performance</p>
        </div>

        {/* Date Range Filter */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mt-3 sm:mt-0">
          <span className="text-xs font-medium text-gray-700">Date Range:</span>
          <div className="flex items-center gap-2">
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="border border-gray-300 rounded-md px-2 py-1 text-xs"
            />
            <span className="text-gray-400 text-xs">to</span>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="border border-gray-300 rounded-md px-2 py-1 text-xs"
            />
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-xs">Total Active Users</p>
              <p className="text-xl font-bold">{overviewStats.totalActiveUsers}</p>
              <p className="text-xs text-blue-100">Staff: {overviewStats.totalStaffUsers} | Guest: {overviewStats.totalGuestUsers}</p>
            </div>
            <Users className="h-8 w-8 text-blue-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-xs">Total Sessions</p>
              <p className="text-xl font-bold">{overviewStats.totalSessions.toLocaleString()}</p>
              <p className="text-xs text-green-100">Avg Duration: 0h 0m</p>
            </div>
            <Sessions className="h-8 w-8 text-green-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-xs">Bandwidth Usage</p>
              <p className="text-xl font-bold">{overviewStats.totalBandwidthGB.toFixed(1)} GB</p>
              <p className="text-xs text-purple-100">Peak Hour: {overviewStats.peakHourUsage}</p>
            </div>
            <Network className="h-8 w-8 text-purple-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-4 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-indigo-100 text-xs">Departments</p>
              <p className="text-xl font-bold">{overviewStats.departmentCount}</p>
              <p className="text-xs text-indigo-100">Active Depts: {departments.filter(d => d.status === 'active').length}</p>
            </div>
            <Network className="h-8 w-8 text-indigo-200" />
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="mb-4">
        <nav className="flex flex-wrap gap-2">
          {[
            { id: "overview", label: "Overview", icon: Activity },
            { id: "users", label: "Users", icon: Users },
            { id: "departments", label: "Departments", icon: Network },
            { id: "nas", label: "NAS Devices", icon: Router }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-full border h-10 text-sm font-medium transition ${activeTab === tab.id
                  ? "bg-blue-600 text-white"
                  : "bg-white text-gray-700 border-gray-300 hover:border-gray-400"
                  }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>


      {/* Tab Content */}
      <div className="space-y-4">
        {activeTab === "overview" && (
          <>
            {/* Usage Trend Chart */}
            <div className="bg-white p-4 rounded-lg shadow border mb-4">
              <h2 className="text-lg font-semibold mb-3">Usage Trends Over Time</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                    <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                    <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12 }}
                      label={{
                        value: 'Date',
                        position: 'insideBottomRight',
                        offset: -5,
                        fontSize: 12
                      }}
                    />

                    <YAxis
                      yAxisId="left"
                      tick={{ fontSize: 12 }}
                      label={{
                        value: 'Bandwidth (GB)',
                        angle: -90,
                        position: 'insideLeft',
                        fontSize: 12
                      }}
                    />

                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      tick={{ fontSize: 12 }}
                      label={{
                        value: 'Sessions / Users',
                        angle: -90,
                        position: 'insideRight',
                        fontSize: 12
                      }}
                    />

                    <Tooltip
                      formatter={(value, name) => {
                        if (name === 'Bandwidth (GB)') return [`${value} GB`, name];
                        if (name === 'Sessions') return [value, name];
                        if (name === 'Active Users') return [value, name];
                        return [value, name];
                      }}
                    />
                    <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                      verticalAlign="top"
                      align="right" />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="usage"
                      stroke="#3b82f6"
                      fillOpacity={0.3}
                      fill="#3b82f6"
                      name="Bandwidth (GB)"
                    />
                    <Area
                      yAxisId="right"
                      type="monotone"
                      dataKey="sessions"
                      stroke="#10b981"
                      fillOpacity={0.3}
                      fill="#10b981"
                      name="Sessions"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="users"
                      stroke="#8b5cf6"
                      strokeWidth={3}
                      name="Active Users"
                      dot={false}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* User Type Distribution & Department Bandwidth Pie Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">User Type Distribution</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Staff Users', value: overviewStats.totalStaffUsers, fill: '#3b82f6' },
                          { name: 'Guest Users', value: overviewStats.totalGuestUsers, fill: '#10b981' },
                        ]}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                        label={false}
                      >
                        {[
                          { name: 'Staff Users', value: overviewStats.totalStaffUsers, fill: '#3b82f6' },
                          { name: 'Guest Users', value: overviewStats.totalGuestUsers, fill: '#10b981' },
                        ].map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip contentStyle={{ fontSize: '12px' }} />
                      <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">Department Bandwidth Distribution</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={departmentPieData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={false}
                      >
                        {departmentPieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} GB`, 'Bandwidth']} />
                      <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* Top Users Pie Chart & Hourly Usage Pattern */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">Top 10 Users by Bandwidth</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {/* Pie Chart */}
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={usersPieData}
                          cx="50%"
                          cy="50%"
                          outerRadius={70}
                          dataKey="value"
                          label={false}
                        >
                          {usersPieData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.fill} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value, name, props) => [
                            `${value} GB`,
                            `${props.payload.displayName} (${props.payload.sessions} sessions)`
                          ]}
                          labelFormatter={() => ''}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Legend Table */}
                  <div className="h-64 overflow-y-auto">
                    <table className="w-full text-xs">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          <th className="px-1 py-1 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                          <th className="px-1 py-1 text-left text-xs font-medium text-gray-500 uppercase">GB</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {usersPieData.map((entry, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-1 py-1">
                              <div className="flex items-center">
                                <div
                                  className="w-2 h-2 rounded mr-1 flex-shrink-0"
                                  style={{ backgroundColor: entry.fill }}
                                />
                                <div className="min-w-0">
                                  <div className="text-xs font-medium text-gray-900 truncate">
                                    {entry.name}
                                  </div>
                                  <div className="text-xs text-gray-500 truncate">
                                    ({entry.department})
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-1 py-1 text-xs text-gray-900">
                              {entry.value}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">Hourly Usage Patterns</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={hourlyUsageData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" tick={{ fontSize: 12 }} />
                      <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                      <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                      <Tooltip contentStyle={{ fontSize: '12px' }} />
                      <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                        verticalAlign="top"
                        align="right" />
                      <Bar yAxisId="left" dataKey="usage" fill="#3b82f6" name="Bandwidth (MB)" />
                      <Line yAxisId="right" type="monotone" dataKey="sessions" stroke="#10b981" strokeWidth={2} name="Sessions" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* Department Performance Bar Chart */}
            <div className="bg-white p-4 rounded-lg shadow border mb-4">
              <h2 className="text-lg font-semibold mb-3">Department Performance Overview</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={departments.map(dept => {
                    const deptStats = individualStats.filter(stat => stat.groupname === dept.package_name);
                    const uniqueUsers = new Set(deptStats.map(s => s.username));
                    const customerUsernames = new Set(customers.map(c => c.username));
                    const staffUsers = Array.from(uniqueUsers).filter(u => customerUsernames.has(u));
                    const guestUsers = Array.from(uniqueUsers).filter(u => !customerUsernames.has(u));
                    const totalBandwidth = deptStats.reduce((sum, stat) =>
                      sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / (1024 * 1024 * 1024);

                    return {
                      name: dept.package_name,
                      staffUsers: staffUsers.length,
                      guestUsers: guestUsers.length,
                      totalBandwidth: parseFloat(totalBandwidth.toFixed(2)),
                      sessions: deptStats.length
                    };
                  }).filter(d => d.sessions > 0)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" textAnchor="middle" tick={{ fontSize: 10 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip contentStyle={{ fontSize: '12px' }} />
                    <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                      verticalAlign="top"
                      align="right" />
                    <Bar dataKey="staffUsers" fill="#3b82f6" name="Staff Users" />
                    <Bar dataKey="guestUsers" fill="#10b981" name="Guest Users" />
                    <Bar dataKey="totalBandwidth" fill="#8b5cf6" name="Bandwidth (GB)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </>
        )}

        {activeTab === "users" && (
          <div className="space-y-4">
            {/* User Analytics Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">Top Users by Bandwidth Usage</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={usersPieData.slice(0, 5)} layout="vertical">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" tick={{ fontSize: 12 }} />
                      <YAxis type="category" dataKey="name" tick={false} />
                      <Tooltip formatter={(value) => [`${value} GB`, 'Bandwidth']} />
                      <Bar
                        dataKey="value"
                        fill="#0ea516ff"
                        radius={[0, 50, 50, 0]}
                      >
                        <LabelList
                          dataKey="displayName"
                          position="insideLeft"
                          style={{ fill: "black", fontSize: 12 }}
                        />
                      </Bar>

                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">User Distribution by Department</h2>
                <div className="h-[256px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={departments.map(dept => {
                      const deptStats = individualStats.filter(stat => stat.groupname === dept.package_name);
                      const uniqueUsers = new Set(deptStats.map(s => s.username));
                      return {
                        name: dept.package_name,
                        users: uniqueUsers.size,
                        sessions: deptStats.length
                      };
                    }).filter(d => d.users > 0)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" textAnchor="middle" tick={{ fontSize: 10 }} />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip contentStyle={{ fontSize: '12px' }} />
                      <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                        verticalAlign="top"
                        align="right" />
                      <Bar dataKey="users" fill="#10b981" name="Users" />
                      <Bar dataKey="sessions" fill="#8b5cf6" name="Sessions" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* User Activity Heatmap */}
            <div className="bg-white p-4 rounded-lg shadow border">
              <h2 className="text-lg font-semibold mb-3">User Activity Throughout the Day</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={hourlyUsageData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip contentStyle={{ fontSize: '12px' }} />
                    <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                      verticalAlign="top"
                      align="right" />
                    <Line type="monotone" dataKey="sessions" stroke="#3b82f6" strokeWidth={3} name="Active Sessions" dot={false} />
                    <Line type="monotone" dataKey="usage" stroke="#10b981" strokeWidth={2} name="Bandwidth (MB)" dot={false} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Top Users Table */}
            <div className="bg-white rounded-lg shadow border">
              <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold">Top Users by Sessions</h2>
                <p className="text-sm text-sm text-gray-600">Users with the most network sessions</p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Bandwidth</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg per Session</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {(() => {
                      const userSessions = new Map<string, any>();

                      individualStats.forEach(stat => {
                        const username = stat.username;
                        if (!userSessions.has(username)) {
                          userSessions.set(username, {
                            username,
                            department: stat.groupname,
                            sessions: 0,
                            bandwidth: 0
                          });
                        }
                        const user = userSessions.get(username);
                        user.sessions++;
                        user.bandwidth += (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets));
                      });

                      return Array.from(userSessions.values())
                        .sort((a, b) => b.sessions - a.sessions)
                        .slice(0, 15)
                        .map((user, index) => (
                          <tr key={user.username} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-bold">
                                {index + 1}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{user.username}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                {user.department}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.sessions}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatBytes(user.bandwidth)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatBytes(user.bandwidth / user.sessions)}
                            </td>
                          </tr>
                        ));
                    })()}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === "departments" && (
          <div className="space-y-4">
            {/* Department Pie Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">Department Bandwidth Usage</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={departmentPieData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={false}
                      >
                        {departmentPieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} GB`, 'Bandwidth']} />
                      <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">Department User Distribution</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={departments.map(dept => {
                      const deptStats = individualStats.filter(stat => stat.groupname === dept.package_name);
                      const uniqueUsers = new Set(deptStats.map(s => s.username));
                      const customerUsernames = new Set(customers.map(c => c.username));
                      const staffUsers = Array.from(uniqueUsers).filter(u => customerUsernames.has(u));
                      const guestUsers = Array.from(uniqueUsers).filter(u => !customerUsernames.has(u));

                      return {
                        name: dept.package_name,
                        staff: staffUsers.length,
                        guest: guestUsers.length,
                        total: uniqueUsers.size
                      };
                    }).filter(d => d.total > 0)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" textAnchor="middle" tick={{ fontSize: 10 }} />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip contentStyle={{ fontSize: '12px' }} />
                      <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                        verticalAlign="top"
                        align="right" />
                      <Bar dataKey="staff" stackId="a" fill="#3b82f6" name="Staff Users" />
                      <Bar dataKey="guest" stackId="a" fill="#10b981" name="Guest Users" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* Department Performance Metrics */}
            <div className="bg-white p-4 rounded-lg shadow border">
              <h2 className="text-lg font-semibold mb-3">Department Performance Comparison</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={departments.map(dept => {
                    const deptStats = individualStats.filter(stat => stat.groupname === dept.package_name);
                    const uniqueUsers = new Set(deptStats.map(s => s.username));
                    const totalBandwidth = deptStats.reduce((sum, stat) =>
                      sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / (1024 * 1024 * 1024);

                    return {
                      name: dept.package_name,
                      users: uniqueUsers.size,
                      sessions: deptStats.length,
                      bandwidth: parseFloat(totalBandwidth.toFixed(2)),
                      avgSessionPerUser: uniqueUsers.size > 0 ? parseFloat((deptStats.length / uniqueUsers.size).toFixed(1)) : 0
                    };
                  }).filter(d => d.sessions > 0)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" textAnchor="middle" tick={{ fontSize: 10 }} />
                    <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                    <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                    <Tooltip contentStyle={{ fontSize: '12px' }} />
                    <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                      verticalAlign="top"
                      align="right" />
                    <Bar yAxisId="left" dataKey="users" fill="#3b82f6" name="Users" />
                    <Bar yAxisId="left" dataKey="sessions" fill="#10b981" name="Sessions" />
                    <Bar yAxisId="right" dataKey="bandwidth" fill="#8b5cf6" name="Bandwidth (GB)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Department Details Table */}
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold">Department Analytics Summary</h2>
                <p className="text-sm text-gray-600">Comprehensive department-wise usage statistics</p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Staff Users</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Guest Users</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total Users</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Staff Bandwidth</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Guest Bandwidth</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total Bandwidth</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Avg/User</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {departments.map(dept => {
                      const deptStats = individualStats.filter(stat => stat.groupname === dept.package_name);
                      if (deptStats.length === 0) return null;

                      const uniqueUsers = new Set(deptStats.map(s => s.username));
                      const customerUsernames = new Set(customers.map(c => c.username));
                      const staffUsers = Array.from(uniqueUsers).filter(u => customerUsernames.has(u));
                      const guestUsers = Array.from(uniqueUsers).filter(u => !customerUsernames.has(u));

                      const staffStats = deptStats.filter(stat => customerUsernames.has(stat.username));
                      const guestStats = deptStats.filter(stat => !customerUsernames.has(stat.username));

                      const staffBandwidth = staffStats.reduce((sum, stat) =>
                        sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0);
                      const guestBandwidth = guestStats.reduce((sum, stat) =>
                        sum + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0);
                      const totalBandwidth = staffBandwidth + guestBandwidth;

                      return (
                        <tr key={dept.package_name} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{dept.package_name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-blue-600 font-medium">
                            {staffUsers.length}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-green-600 font-medium">
                            {guestUsers.length}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900 font-bold">
                            {uniqueUsers.size}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                            {deptStats.length}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-blue-600">
                            {formatBytes(staffBandwidth)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-green-600">
                            {formatBytes(guestBandwidth)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-purple-600 font-bold">
                            {formatBytes(totalBandwidth)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                            {formatBytes(totalBandwidth / Math.max(uniqueUsers.size, 1))}
                          </td>
                        </tr>
                      );
                    }).filter(Boolean)}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === "nas" && (
          <div className="space-y-4">
            {/* NAS Performance Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">NAS Device Session Distribution</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={nasAnalytics.map((nas, index) => ({
                          name: nas.nasName,
                          value: nas.totalSessions,
                          fill: ["#3b82f6", "#10b981", "#8b5cf6", "#ef4444", "#06b6d4", "#84cc16"][index % 6]
                        }))}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={false}
                      >
                        {nasAnalytics.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={["#3b82f6", "#10b981", "#8b5cf6", "#ef4444", "#06b6d4", "#84cc16"][index % 6]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}`, 'Sessions']} />
                      <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-3">NAS Bandwidth Usage</h2>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={nasAnalytics} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" tick={{ fontSize: 12 }} />
                      <YAxis type="category" dataKey="nasName" tick={{ fontSize: 12 }} />
                      <Tooltip formatter={(value) => [`${value} GB`, 'Bandwidth']} />
                      <Bar dataKey="totalBandwidth" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* NAS Performance Comparison */}
            <div className="bg-white p-4 rounded-lg shadow border">
              <h2 className="text-lg font-semibold mb-3">NAS Device Performance Comparison</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={nasAnalytics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="nasName" textAnchor="middle" tick={{ fontSize: 10 }} />
                    <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                    <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                    <Tooltip contentStyle={{ fontSize: '12px' }} />
                    <Legend iconSize={12} wrapperStyle={{ fontSize: '12px' }} layout="horizontal"
                      verticalAlign="top"
                      align="right" />
                    <Bar yAxisId="left" dataKey="totalSessions" fill="#3b82f6" name="Total Sessions" />
                    <Bar yAxisId="right" dataKey="totalBandwidth" fill="#10b981" name="Bandwidth (GB)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* NAS Device Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {nasDevices.length}
                </div>
                <div className="text-sm text-blue-600">Total NAS Devices</div>
                <div className="text-xs text-gray-500 mt-1">
                  {nasAnalytics.filter(nas => nas.totalSessions > 0).length} Active
                </div>
              </div>
              <div className="bg-green-50 p-6 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {nasAnalytics.reduce((sum, nas) => sum + nas.totalSessions, 0).toLocaleString()}
                </div>
                <div className="text-sm text-green-600">Total Sessions</div>
                <div className="text-xs text-gray-500 mt-1">
                  Across all NAS devices
                </div>
              </div>
              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {formatBytes(nasAnalytics.reduce((sum, nas) => sum + nas.totalBandwidth, 0) * 1024 * 1024 * 1024)}
                </div>
                <div className="text-sm text-purple-600">Total Bandwidth</div>
                <div className="text-xs text-gray-500 mt-1">
                  Combined usage
                </div>
              </div>
            </div>

            {/* NAS Details Table */}
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold">NAS Device Analytics</h2>
                <p className="text-sm text-gray-600">Network Access Server performance metrics and statistics</p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NAS Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total Sessions</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Bandwidth Usage</th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Avg per Session</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {nasAnalytics.length > 0 ? (
                      nasAnalytics.map((nas, index) => (
                        <tr key={nas.nasIP} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{nas.nasName}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 font-mono">{nas.nasIP}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              {nas.vendor}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900 font-medium">
                            {nas.totalSessions.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-purple-600 font-medium">
                            {formatBytes(nas.totalBandwidth * 1024 * 1024 * 1024)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                            {nas.totalSessions > 0 ? formatBytes((nas.totalBandwidth * 1024 * 1024 * 1024) / nas.totalSessions) : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">
                            {nas.description}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={7} className="px-6 py-8 text-center text-gray-500">
                          <div className="flex flex-col items-center">
                            <Network className="h-12 w-12 text-gray-300 mb-2" />
                            <p className="text-lg font-medium">No NAS devices found</p>
                            <p className="text-sm">No network access servers have been configured or have usage data.</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="text-gray-700">Loading analytics data...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}